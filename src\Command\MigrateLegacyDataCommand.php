<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use App\Entity\Document;
use App\Entity\Visa;
use App\Entity\Commentaire;
use App\Entity\ReleasedPackage;
use App\Entity\Project;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Repository\ProjectRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;
use Symfony\Component\Workflow\Registry;

#[AsCommand(
    name: 'app:migrate-legacy',
    description: 'Migre les données de l’ancienne BD vers le nouveau schéma'
)]
class MigrateLegacyDataCommand extends Command
{
    protected static $defaultName = 'app:migrate-legacy';

    // Cache pour éviter les recherches répétées d'utilisateurs
    private array $userCache = [];
    private array $userIdCache = []; // Cache des IDs utilisateurs pour SQL brut
    private ?User $adminUser = null;
    private array $stateCounts = [];
    private array $packageDataCache = []; // Cache des données de packages
    private array $projectIdCache = []; // Cache des IDs de projets

    public function __construct(
        #[ConnectionName('legacy')]
        private Connection $oldDb,
        private EntityManagerInterface $em,
        private UserRepository $users,
        private ProjectRepository $projects,
        private Registry $workflowRegistry
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('limit', null, InputOption::VALUE_OPTIONAL, 'Limiter le nombre d’enregistrements à migrer');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $limit = $input->getOption('limit');

        // Optimisations mémoire pour la migration
        $this->optimizeForMigration($output);

        // Mapping pour associer les anciens IDs aux nouveaux packages
        $packageMapping = [];

        // 0. Nettoyer les tables existantes pour éviter les doublons
        $this->cleanExistingData($output);

        // 0.5. Initialiser les caches
        $this->initializeAllCaches($output);

        // 1. Créer les projets manquants
        $this->createMissingProjectsOptimized($output);

        // 3. Migrer TOUS les ReleasedPackage avec SQL brut optimisé
        $packageMapping = $this->migratePackagesOptimized($output);

        // 4. Migrer les Documents avec optimisations
            // on n’a plus le format prénom.nom, on garde juste le nom (avant l’espace)
            // Recherche flexible des utilisateurs avec cache
            $owner = $this->findUserWithCache($r['Rel_Pack_Owner'], $output);
            $verif = $this->findUserWithCache($r['Verif_Req_Owner'], $output);
            $valid = $this->findUserWithCache($r['BE_3_Req_Owner'], $output);

            // Seul le owner est obligatoire, verif et valid peuvent être optionnels
            if (!$owner) {
                $output->writeln(sprintf(
                    '<error>Utilisateur introuvable pour package %s : owner=%s, verif=%s, valid=%s</error>',
                    $r['Rel_Pack_Num'], $r['Rel_Pack_Owner'], $r['Verif_Req_Owner'], $r['BE_3_Req_Owner']
                ));
                continue;
            }

            // Log des utilisateurs manquants mais non bloquants
            if (!$verif && !empty($r['Verif_Req_Owner'])) {
                $output->writeln(sprintf(
                    '<comment>Verif introuvable pour package %s : verif=%s</comment>',
                    $r['Rel_Pack_Num'], $r['Verif_Req_Owner']
                ));
            }
            if (!$valid && !empty($r['BE_3_Req_Owner'])) {
                $output->writeln(sprintf(
                    '<comment>Valid introuvable pour package %s : valid=%s</comment>',
                    $r['Rel_Pack_Num'], $r['BE_3_Req_Owner']
                ));
            }

            $proj = $this->projects->findOneByOtp($r['Project']);
            if (!$proj) {
                $output->writeln(sprintf(
                    '<error>Project OTP introuvable : %s (package %s)</error>',
                    $r['Project'], $r['Rel_Pack_Num']
                ));
                continue;
            }

            // Créer l'entité ReleasedPackage avec l'ORM
            $package = new ReleasedPackage();
            $package->setOwner($owner);
            $package->setVerif($verif);
            $package->setValid($valid);
            $package->setProjectRelation($proj);
            $package->setDescription($r['Observations']);
            $package->setActivity($r['Activity']);
            $package->setEx($r['Ex']);

            // Conversion des dates si elles ne sont pas nulles et valides
            if (!empty($r['Reservation_Date']) && $this->isValidDate($r['Reservation_Date'])) {
                $package->setReservationDate(new \DateTime($r['Reservation_Date']));
            }
            if (!empty($r['Creation_Date']) && $this->isValidDate($r['Creation_Date'])) {
                $package->setCreationDate(new \DateTime($r['Creation_Date']));
            }

            $this->em->persist($package);

            // Stocker les données de visa du package pour les appliquer aux documents plus tard
            $packageVisaData[$r['Rel_Pack_Num']] = $r;
            $packageCount++;

            // Flush périodique pour éviter les problèmes de mémoire
            if ($packageCount % 100 === 0) {
                $this->em->flush();

                // Stocker l'ID après flush (quand l'ID est généré)
                $packageMapping[$r['Rel_Pack_Num']] = $package->getId();

                $this->em->clear(); // Libérer la mémoire
                $output->writeln("<comment>Flush packages: $packageCount traités</comment>");

                // Recharger l'utilisateur Admin et vider le cache après clear()
                $this->reloadAfterClear();
            } else {
                // Stocker l'ID immédiatement si pas de flush
                $packageMapping[$r['Rel_Pack_Num']] = $package;
            }
        }

        // Flush des packages avant de migrer les documents
        $this->em->flush();

        // Finaliser le mapping : convertir toutes les entités en IDs
        foreach ($packageMapping as $oldId => $packageOrId) {
            if (is_object($packageOrId)) {
                $packageMapping[$oldId] = $packageOrId->getId();
            }
        }

        $output->writeln('<info>Packages migrés avec succès.</info>');

        // 4. Migrer les Documents via ORM (appliquer la limite ici)
        $sql = 'SELECT * FROM tbl_released_drawing' . ($limit ? ' LIMIT ' . (int)$limit : '');
        $draws = $this->oldDb->fetchAllAssociative($sql);

        $documentCount = 0;
        foreach ($draws as $d) {
            // Vérifier que le package existe dans notre mapping
            if (!isset($packageMapping[$d['Rel_Pack_Num']])) {
                $output->writeln(sprintf(
                    '<e>Package introuvable pour document %s (Rel_Pack_Num: %s)</e>',
                    $d['Reference'], $d['Rel_Pack_Num']
                ));
                continue;
            }

            $doc = new Document();
            // Récupérer le package par son ID depuis le mapping
            $packageId = $packageMapping[$d['Rel_Pack_Num']];
            $package = $this->em->getReference(ReleasedPackage::class, $packageId);
            $doc->setRelPack($package);

            // Fonction helper pour setter les valeurs seulement si elles existent
            $setSafe = function($setter, $key, $transform = null) use ($doc, $d) {
                if (isset($d[$key])) {
                    $value = $transform ? $transform($d[$key]) : $d[$key];
                    $doc->$setter($value);
                }
            };

            $setSafe('setReference', 'Reference');
            $setSafe('setRefRev', 'Ref_Rev');
            $setSafe('setRefTitleFra', 'Ref_Title');
            $setSafe('setProdDraw', 'Prod_Draw');
            $setSafe('setProdDrawRev', 'Prod_Draw_Rev');
            $setSafe('setAlias', 'Alias');
            $setSafe('setDocType', 'Doc_Type');
            $setSafe('setMaterialType', 'Material_Type');
            $setSafe('setProcType', 'Proc_Type');
            $setSafe('setInventoryImpact', 'Inventory_Impact');
            $setSafe('setCustDrawing', 'Cust_Drawing');
            $setSafe('setCustDrawingRev', 'Cust_Drawing_Rev');
            $setSafe('setAction', 'Action');
            $setSafe('setEx', 'Ex');
            $setSafe('setWeight', 'Weight');
            $setSafe('setWeightUnit', 'Weight_Unit');
            $setSafe('setPlatingSurface', 'Plating_Surface');
            $setSafe('setPlatingSurfaceUnit', 'Plating_Surface_Unit');
            $setSafe('setInternalMachRec', 'Internal_Mach_Rec', function($v) { return (bool)$v; });
            $setSafe('setCls', 'CLS');
            $setSafe('setMoq', 'MOQ');
            $setSafe('setProductCode', 'Product_Code');
            $setSafe('setProdAgent', 'Prod_Agent');
            $setSafe('setMof', 'MOF');
            $setSafe('setCommodityCode', 'Commodity_Code');
            $setSafe('setPurchasingGroup', 'Purchasing_Group');
            $setSafe('setMatProdType', 'Mat_Prod_Type');
            $setSafe('setUnit', 'Unit');
            $setSafe('setLeadtime', 'leadtime');
            $setSafe('setPrisDans1', 'Pris_Dans1');
            $setSafe('setPrisDans2', 'Pris_Dans2');
            $setSafe('setEccn', 'ECCN');
            $setSafe('setRdo', 'RDO');
            $setSafe('setHts', 'HTS');
            $setSafe('setFia', 'FIA');
            $setSafe('setMetroTime', 'Metro_Time');
            // MetroControl doit être un array, pas une string
            if (!empty($d['Metro_Control'])) {
                // Si c'est une string, on la convertit en array
                $metroControlArray = is_string($d['Metro_Control']) ? [$d['Metro_Control']] : $d['Metro_Control'];
                $doc->setMetroControl($metroControlArray);
            }
            // QInspection doit être un array
            if (!empty($d['Q_Inspection'])) {
                $qInspectionArray = is_string($d['Q_Inspection']) ? [$d['Q_Inspection']] : $d['Q_Inspection'];
                $doc->setQInspection($qInspectionArray);
            }
            $setSafe('setQDynamization', 'Q_Dynamization');
            // QDocRec doit être un array
            if (isset($d['Q_Doc_Req']) && !empty($d['Q_Doc_Req'])) {
                $qDocRecArray = is_string($d['Q_Doc_Req']) ? [$d['Q_Doc_Req']] : $d['Q_Doc_Req'];
                $doc->setQDocRec($qDocRecArray);
            }
            $setSafe('setQControlRouting', 'Q_Control_Routing');
            $setSafe('setCriticalComplete', 'Critical_Complete');
            $setSafe('setSwitchAletiq', 'SWITCH_ALETIQ', function($v) { return (bool)$v; });
            $setSafe('setIdAletiq', 'ID_ALETIQ');
            $setSafe('setMaterial', 'Material');
            // DocImpact est obligatoire, valeur par défaut 0 si manquant
            $docImpact = isset($d['Doc_Impact']) ? (int)$d['Doc_Impact'] : 0;
            $doc->setDocImpact($docImpact);

            $this->em->persist($doc);

            // Commentaires
            if (!empty($d['Requestor_Comments'])) {
                $c = new Commentaire();
                $c->setUser($this->adminUser ?: $doc->getSuperviseur());
                $c->setDocuments($doc);
                $c->setType('request');
                $c->setCommentaire($d['Requestor_Comments']);
                $c->setCreatedAt(new \DateTimeImmutable()); // Champ obligatoire
                $this->em->persist($c);
            }

            // Créer les visas et appliquer le workflow
            $this->migrateDocumentVisasAndWorkflow($doc, $d, $output);

            $documentCount++;

            // Flush moins fréquent pour éviter les problèmes de mémoire
            if ($documentCount % 20 === 0) {
                $this->em->flush();
                $this->em->clear(); // Libérer la mémoire
                $output->writeln("<comment>Flush documents: $documentCount traités</comment>");

                // Recharger l'utilisateur Admin et vider le cache après clear()
                $this->reloadAfterClear();

                // Forcer le garbage collector plus agressivement
                gc_collect_cycles();

                // Afficher l'utilisation mémoire actuelle
                $memoryUsage = memory_get_usage(true) / 1024 / 1024;
                $output->writeln("<comment>Mémoire utilisée: " . round($memoryUsage, 2) . " MB</comment>");
            }
        }

        $this->em->flush();
        $output->writeln('<info>Documents migrés avec succès.</info>');

        // 5. Appliquer les visas des packages aux documents (temporairement désactivé)
        // $output->writeln('<info>Application des visas de packages...</info>');
        // $this->applyPackageVisasToDocuments($packageVisaData, $packageMapping, $output);

        $this->em->flush();

        // Afficher le résumé de la distribution des états
        $this->displayStateSummary($output);

        $output->writeln('<info>Migration terminée.</info>');
        return Command::SUCCESS;
    }

    private function createMissingProjects(OutputInterface $output): void
    {
        // Récupérer tous les codes de projets uniques de l'ancienne base
        $projectCodes = $this->oldDb->fetchAllAssociative('SELECT DISTINCT Project FROM tbl_released_package WHERE Project IS NOT NULL');

        $createdCount = 0;
        foreach ($projectCodes as $row) {
            $otpCode = $row['Project'];

            // Vérifier si le projet existe déjà
            $existingProject = $this->projects->findOneByOtp($otpCode);
            if (!$existingProject) {
                // Créer le projet manquant
                $project = new Project();
                $project->setOTP($otpCode);
                $project->setTitle("Projet migré: " . $otpCode);
                $project->setStatus('active');

                $this->em->persist($project);
                $createdCount++;

                $output->writeln(sprintf('<info>Projet créé: %s</info>', $otpCode));
            }
        }

        if ($createdCount > 0) {
            $this->em->flush();
            $output->writeln(sprintf('<info>%d projets créés.</info>', $createdCount));
        } else {
            $output->writeln('<info>Aucun projet à créer.</info>');
        }
    }

    private function isValidDate(?string $date): bool
    {
        if (empty($date)) {
            return false;
        }

        // Vérifier si la date contient des années négatives ou invalides
        if (strpos($date, '-0001') !== false || strpos($date, '0000') !== false) {
            return false;
        }

        try {
            $dateTime = new \DateTime($date);
            // Vérifier que l'année est raisonnable (après 1900)
            return $dateTime->format('Y') >= 1900;
        } catch (\Exception $e) {
            return false;
        }
    }

    private function extractLastName(?string $fullName): ?string
    {
        if (empty($fullName)) {
            return null;
        }

        $fullName = trim($fullName);

        // Extraire le nom de famille (avant le premier espace)
        // Ex: "PISSOT T." -> "PISSOT", "GALIPAUD JF." -> "GALIPAUD"
        $parts = explode(' ', $fullName);
        return !empty($parts[0]) ? $parts[0] : null;
    }

    private function optimizeForMigration(OutputInterface $output): void
    {
        $output->writeln('<info>Application des optimisations mémoire...</info>');

        // 1. Augmenter la limite de mémoire PHP
        ini_set('memory_limit', '4G');
        $output->writeln('<comment>Limite mémoire PHP augmentée à 4G</comment>');

        // 2. Désactiver le mode debug de Doctrine pour réduire la consommation mémoire
        $config = $this->em->getConfiguration();
        if (method_exists($config, 'getSQLLogger') && $config->getSQLLogger()) {
            $config->setSQLLogger(null);
            $output->writeln('<comment>SQL Logger Doctrine désactivé</comment>');
        }

        // 3. Configurer le garbage collector
        gc_enable();
        $output->writeln('<comment>Garbage Collector activé</comment>');

        $output->writeln('<info>Optimisations appliquées avec succès.</info>');
    }

    private function cleanExistingData(OutputInterface $output): void
    {
        $output->writeln('<info>Nettoyage des données existantes...</info>');

        // Supprimer dans l'ordre inverse des dépendances
        // 1. Supprimer les visas (dépendent des documents)
        $this->em->createQuery('DELETE FROM App\Entity\Visa')->execute();
        $output->writeln('<comment>Visas supprimés.</comment>');

        // 2. Supprimer les commentaires (dépendent des documents)
        $this->em->createQuery('DELETE FROM App\Entity\Commentaire c WHERE c.documents IS NOT NULL')->execute();
        $output->writeln('<comment>Commentaires de documents supprimés.</comment>');

        // 3. Supprimer les documents (dépendent des packages)
        $this->em->createQuery('DELETE FROM App\Entity\Document')->execute();
        $output->writeln('<comment>Documents supprimés.</comment>');

        // 4. Supprimer les packages (dépendent des projets et utilisateurs)
        $this->em->createQuery('DELETE FROM App\Entity\ReleasedPackage')->execute();
        $output->writeln('<comment>Packages supprimés.</comment>');

        // 5. Supprimer les projets créés par migration (garder ceux créés manuellement)
        $this->em->createQuery('DELETE FROM App\Entity\Project p WHERE p.Title LIKE :pattern')
            ->setParameter('pattern', 'Projet migré:%')
            ->execute();
        $output->writeln('<comment>Projets migrés supprimés.</comment>');

        $this->em->flush();
        $output->writeln('<info>Nettoyage terminé.</info>');
    }

    private function initializeUserCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des utilisateurs...</info>');

        // Vider le cache
        $this->userCache = [];

        // Charger l'utilisateur Admin
        $this->adminUser = $this->users->findOneBy(['nom' => 'Admin']);
        if ($this->adminUser) {
            $output->writeln('<info>Utilisateur Admin trouvé pour fallback.</info>');
        } else {
            $output->writeln('<error>Aucun utilisateur Admin trouvé !</error>');
        }

        $output->writeln('<info>Cache initialisé.</info>');
    }

    private function findUserWithCache(?string $raw, OutputInterface $output): ?User
    {
        // Si vide, retourner null directement
        if (empty($raw)) {
            return null;
        }

        // Vérifier le cache d'abord
        $cacheKey = trim($raw);
        if (isset($this->userCache[$cacheKey])) {
            $cachedUser = $this->userCache[$cacheKey];
            if ($cachedUser === 'ADMIN_FALLBACK') {
                // Réduire la verbosité pour les cache hits
                return $this->adminUser;
            } elseif ($cachedUser === 'NOT_FOUND') {
                return null;
            } else {
                return $cachedUser;
            }
        }

        // Pas en cache, faire la recherche complète
        $output->writeln("<info>→ Recherche user pour « $raw » (pas en cache)</info>");
        $user = $this->findUserFlexible($raw, $output);

        // Mettre en cache le résultat
        if ($user === $this->adminUser) {
            $this->userCache[$cacheKey] = 'ADMIN_FALLBACK';
        } elseif ($user === null) {
            $this->userCache[$cacheKey] = 'NOT_FOUND';
        } else {
            $this->userCache[$cacheKey] = $user;
        }

        return $user;
    }

    private function reloadAfterClear(): void
    {
        // Recharger l'utilisateur Admin après clear()
        if ($this->adminUser) {
            $adminId = $this->adminUser->getId();
            $this->adminUser = $this->users->find($adminId);
        }

        // Vider le cache des utilisateurs car les entités ne sont plus valides
        $this->userCache = [];
    }

    private function findUserFlexible(?string $raw, OutputInterface $output): ?User
    {
        $output->writeln("<info>→ Recherche user pour « $raw »</info>");

        if (empty($raw)) {
            $output->writeln("  (raw vide)");
            return null;
        }

        // 1) Normalisation
        $clean = $this->normalizeName($raw);
        $output->writeln("  Normalisé ➜ « $clean »");

        $parts    = preg_split('/\s+/', $clean);
        $lastName = $parts[0] ?? '';
        $firstPart = $parts[1] ?? '';
        $output->writeln("  Nom extrait = « $lastName », initiales/prénom = « $firstPart »");

        // 2) Exact match
        $allUsers = $this->users->findAll();
        $output->writeln("  Total users en BDD: ".count($allUsers));

        $matches = [];
        foreach ($allUsers as $u) {
            if ($this->normalizeName($u->getNom()) === $lastName) {
                $matches[] = $u;
            }
        }
        $output->writeln("  Exact match nom: ".count($matches)." résultat(s)");

        if (count($matches) === 1) {
            $output->writeln("    → on retourne " . $matches[0]->getNom() . " " . $matches[0]->getPrenom());
            return $matches[0];
        }

        // 3) Initiales prénom
        if ($firstPart && count($matches) > 1) {
            $output->writeln("  Tentative correspondance initiales prénom…");
            foreach ($matches as $u) {
                $prenomNorm = $this->normalizeName($u->getPrenom() ?? '');
                $initials = '';
                foreach (preg_split('/\s+/', $prenomNorm) as $p) {
                    $initials .= mb_substr($p, 0, 1, 'UTF-8');
                }
                if ($initials === $firstPart) {
                    $output->writeln("    → matched by initials: " . $u->getNom() . " " . $u->getPrenom());
                    return $u;
                }
            }
        }

        // 4) Inclusion partielle
        $output->writeln("  Recherche inclusion partielle du nom…");
        foreach ($allUsers as $u) {
            if (mb_stripos($this->normalizeName($u->getNom()), $lastName, 0, 'UTF-8') !== false) {
                $output->writeln("    → matched by contains: " . $u->getNom() . " " . $u->getPrenom());
                return $u;
            }
        }

        // 5) Fallback fuzzy (Levenshtein)
        $output->writeln("  Fallback fuzzy (Levenshtein)…");
        $closest = null;
        $minDist = PHP_INT_MAX;
        foreach ($allUsers as $u) {
            $dist = levenshtein($lastName, $this->normalizeName($u->getNom()));
            if ($dist < $minDist) {
                $minDist = $dist;
                $closest = $u;
            }
        }
        $output->writeln("    Distance min = $minDist pour ". $closest->getNom());
        if ($closest && $minDist <= 1) { // Seuil plus strict pour éviter les mauvaises correspondances
            $output->writeln("    → on accepte fuzzy: " . $closest->getNom() . " " . $closest->getPrenom());
            return $closest;
        }

        // 6) Fallback vers l'utilisateur Admin
        $output->writeln("    Pas de match, fallback vers Admin");
        if ($this->adminUser) {
            $output->writeln("    → fallback Admin: " . $this->adminUser->getNom() . " " . $this->adminUser->getPrenom());
            return $this->adminUser;
        }

        $output->writeln("    Aucun utilisateur Admin trouvé !");
        return null;
    }

    /**
     * Nouvelle méthode pour migrer les visas et déterminer les états selon les conditions legacy
     */
    private function migrateDocumentVisasAndWorkflow(Document $doc, array $legacyData, OutputInterface $output): void
    {
        // 1. Créer tous les visas sans appliquer le workflow
        $this->createAllVisasWithoutWorkflow($doc, $legacyData, $output);

        // 2. Déterminer les états selon les conditions SQL legacy
        $this->determineStatesFromLegacyConditions($doc, $legacyData, $output);

        $output->writeln("<comment>Document {$legacyData['Reference']} → États déterminés selon conditions legacy</comment>");
    }

    /**
     * Créer tous les visas sans appliquer le workflow
     */
    private function createAllVisasWithoutWorkflow(Document $doc, array $legacyData, OutputInterface $output): void
    {
        // 1. Créer les visas du package
        $packageVisas = $this->getPackageVisasInOrder($legacyData);
        foreach ($packageVisas as $visaData) {
            $this->createSingleVisa($doc, $visaData, $output);
        }

        // 2. Créer les visas legacy
        $legacyVisas = $this->getLegacyVisasInOrder($legacyData);
        foreach ($legacyVisas as $visaData) {
            $this->createSingleVisa($doc, $visaData, $output);
        }

        // 3. Flush pour que tous les visas soient disponibles
        $this->em->flush();
        $this->em->refresh($doc);
    }

    /**
     * Créer un visa unique sans appliquer le workflow
     */
    private function createSingleVisa(Document $doc, array $visaData, OutputInterface $output): void
    {
        // Créer le visa
        $visa = new Visa();
        $visa->setName($visaData['name']);
        $visa->setDateVisa(\DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $visaData['date']) ?: new \DateTimeImmutable($visaData['date']));
        $visa->setStatus('valid');
        $visa->setReleasedDrawing($doc);

        if ($this->adminUser) {
            $visa->setValidator($this->adminUser);
        }

        $this->em->persist($visa);
        $output->writeln("<info>  → Visa créé: {$visaData['name']} ({$visaData['date']})</info>");
    }

    /**
     * Récupérer les visas legacy dans l'ordre chronologique
     */
    private function getLegacyVisasInOrder(array $legacyData): array
    {
        // Mapping correct des noms de visas (basé sur VisaChecker.php)
        $visaMapping = [
            'Product' => 'visa_produit',
            'Project' => 'visa_Project',
            'Quality' => 'visa_Quality',
            'Inventory' => 'visa_Logistique', // Legacy Inventory maps to Logistique
            'Prod' => 'visa_prod',
            'Method' => 'visa_Methode_assemblage',
            'Supply' => 'visa_Planning', // Legacy Supply maps to Planning
            'Metro' => 'visa_Metro',
            'PUR_1' => 'visa_Achat_Rfq', // Legacy PUR_1 maps to Achat_Rfq
            'PUR_2' => 'visa_Achat_F30', // Legacy PUR_2 maps to Achat_F30
            'PUR_3' => 'visa_Achat_FIA', // Legacy PUR_3 maps to Achat_FIA
            'PUR_5' => 'visa_Achat_Hts', // Legacy PUR_5 maps to Achat_Hts
            'GID' => 'visa_GID',
            'GID_2' => 'visa_Core_Data', // Legacy GID_2 maps to Core_Data
            'ROUTING_ENTRY' => 'visa_Prod_Data', // Legacy ROUTING_ENTRY maps to Prod_Data
            'Finance' => 'visa_Costing', // Legacy Finance maps to Costing
        ];

        // Collecter tous les visas avec leurs dates pour les trier chronologiquement
        $visasWithDates = [];
        foreach ($visaMapping as $visaKey => $visaName) {
            $visaCol = "VISA_{$visaKey}";
            $dateCol = "DATE_{$visaKey}";

            if (isset($legacyData[$visaCol]) && !empty($legacyData[$visaCol]) &&
                isset($legacyData[$dateCol]) && $this->isValidDate($legacyData[$dateCol])) {

                $visasWithDates[] = [
                    'name' => $visaName,
                    'date' => $legacyData[$dateCol],
                    'legacy_key' => $visaKey
                ];
            }
        }

        // Trier par date chronologique
        usort($visasWithDates, function($a, $b) {
            return strtotime($a['date']) <=> strtotime($b['date']);
        });

        return $visasWithDates;
    }

    /**
     * Déterminer les états selon les conditions SQL legacy exactes
     */
    private function determineStatesFromLegacyConditions(Document $doc, array $legacyData, OutputInterface $output): void
    {
        $documentVisas = $doc->getVisasArray();
        $output->writeln("<info>  → Visas présents: " . implode(', ', $documentVisas) . "</info>");



        // Récupérer les données du package pour les conditions
        $packageData = $this->getPackageDataForConditions($legacyData);

        // Déterminer tous les états où le document devrait être selon les conditions legacy
        $activeStates = [];

        // Vérifier chaque condition dans l'ordre de priorité
        // IMPORTANT: Les conditions SQL montrent les documents QUI SONT dans cet état,
        // pas ceux qui ont terminé cet état
        if ($this->checkBE0Condition($packageData)) {
            $activeStates['BE_0'] = 1;
        }
        elseif ($this->checkBE1Condition($packageData)) {
            $activeStates['BE_1'] = 1;
        }
        elseif ($this->checkBECondition($packageData)) {
            $activeStates['BE'] = 1;
        }
        if ($this->checkProductCondition($legacyData, $packageData)) {
            $activeStates['Produit'] = 1;
        }
        if ($this->checkInventoryCondition($legacyData, $packageData)) {
            $activeStates['Qual_Logistique'] = 1;
        }
        if ($this->checkQualityCondition($legacyData, $packageData)) {
            $activeStates['Quality'] = 1;
        }
        if ($this->checkProjectCondition($legacyData, $packageData)) {
            $activeStates['Project'] = 1;
        }
        if ($this->checkMetroCondition($legacyData)) {
            $activeStates['Metro'] = 1;
        }
        if ($this->checkQProdCondition($legacyData)) {
            $activeStates['QProd'] = 1;
        }
        if ($this->checkAssemblyCondition($legacyData)) {
            $activeStates['Assembly'] = 1;
        }
        if ($this->checkMethodCondition($legacyData)) {
            $activeStates['Methode_assemblage'] = 1;
        }
        if ($this->checkMachiningCondition($legacyData)) {
            $activeStates['Machining'] = 1;
        }
        if ($this->checkMoldingCondition($legacyData)) {
            $activeStates['Molding'] = 1;
        }
        if ($this->checkAchatRfqCondition($legacyData)) {
            $activeStates['Achat_Rfq'] = 1;
        }
        if ($this->checkAchatF30Condition($legacyData)) {
            $activeStates['Achat_F30'] = 1;
        }
        if ($this->checkAchatFIACondition($legacyData)) {
            $activeStates['Achat_FIA'] = 1;
        }
        if ($this->checkAchatRohsReachCondition($legacyData)) {
            $activeStates['Achat_RoHs_REACH'] = 1;
        }
        if ($this->checkAchatHtsCondition($legacyData)) {
            $activeStates['Achat_Hts'] = 1;
        }
        if ($this->checkPlanningCondition($legacyData)) {
            $activeStates['Planning'] = 1;
        }
        if ($this->checkIndusCondition($legacyData)) {
            $activeStates['Indus'] = 1;
        }
        if ($this->checkCoreDataCondition($legacyData, $packageData)) {
            $activeStates['Core_Data'] = 1;
        }
        if ($this->checkProdDataCondition($legacyData)) {
            $activeStates['Prod_Data'] = 1;
        }
        if ($this->checkLaboCondition($legacyData)) {
            $activeStates['methode_Labo'] = 1;
        }
        if ($this->checkGIDCondition($legacyData)) {
            $activeStates['GID'] = 1;
        }
        if ($this->checkCostingCondition($legacyData)) {
            $activeStates['Costing'] = 1;
        }

        // Si aucun état trouvé, mettre en BE_0 par défaut
        if (empty($activeStates)) {
            $activeStates['BE_0'] = 1;
        }

        // Appliquer les états
        $doc->setCurrentSteps($activeStates);

        // Créer les timestamps appropriés
        $this->createStateTimestampsFromAllVisas($doc, $legacyData);

        $this->em->flush();

        $finalStates = array_keys($activeStates);
        $output->writeln("<info>  → États finaux: " . implode(', ', $finalStates) . "</info>");

        // Compter les états pour le résumé final
        $this->countStatesForSummary($activeStates);
    }

    /**
     * Compter les états pour le résumé final
     */
    private function countStatesForSummary(array $activeStates): void
    {
        static $stateCounts = [];

        foreach (array_keys($activeStates) as $state) {
            $stateCounts[$state] = ($stateCounts[$state] ?? 0) + 1;
        }

        // Stocker pour affichage final
        $this->stateCounts = $stateCounts;
    }

    /**
     * Afficher le résumé des états
     */
    public function displayStateSummary(OutputInterface $output): void
    {
        if (empty($this->stateCounts)) {
            return;
        }

        $output->writeln('<info>=== RÉSUMÉ DE LA DISTRIBUTION DES ÉTATS ===</info>');

        // Trier par nombre de documents (décroissant)
        arsort($this->stateCounts);

        foreach ($this->stateCounts as $state => $count) {
            $output->writeln("<info>$state: $count documents</info>");
        }

        $total = array_sum($this->stateCounts);
        $output->writeln("<info>TOTAL: $total placements d'états</info>");
    }

    /**
     * Récupérer les données du package pour les conditions
     */
    private function getPackageDataForConditions(array $legacyData): ?array
    {
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        if (!$packageNum) {
            return null;
        }

        try {
            return $this->oldDb->fetchAssociative(
                'SELECT Creation_VISA, VISA_BE_2, VISA_BE_3, Project, Activity, Creation_Date, Reservation_Date, DATE_BE_2, DATE_BE_3 FROM tbl_released_package WHERE Rel_Pack_Num = ?',
                [$packageNum]
            );
        } catch (\Exception $e) {
            return null;
        }
    }

    // ===== CONDITIONS BE =====

    /**
     * BE_0_condition: Aucun visa dans le package (CORRECTION - ne pas accepter packageData null)
     */
    private function checkBE0Condition(?array $packageData): bool
    {
        if (!$packageData) {
            return false; // Si pas de package data, ce n'est pas un document BE_0 valide
        }

        $result = trim($packageData['Creation_VISA'] ?? '') === '' &&
                 trim($packageData['VISA_BE_2'] ?? '') === '' &&
                 trim($packageData['VISA_BE_3'] ?? '') === '';

        return $result;
    }

    /**
     * BE_2_condition: Creation_VISA présent mais pas les autres
     */
    private function checkBE1Condition(?array $packageData): bool
    {
        if (!$packageData) return false;

        return trim($packageData['Creation_VISA'] ?? '') !== '' &&
               trim($packageData['VISA_BE_2'] ?? '') === '' &&
               trim($packageData['VISA_BE_3'] ?? '') === '';
    }

    /**
     * BE_condition: Creation_VISA et VISA_BE_2 présents mais pas VISA_BE_3
     */
    private function checkBECondition(?array $packageData): bool
    {
        if (!$packageData) return false;

        return trim($packageData['Creation_VISA'] ?? '') !== '' &&
               trim($packageData['VISA_BE_2'] ?? '') !== '' &&
               trim($packageData['VISA_BE_3'] ?? '') === '';
    }

    // ===== CONDITIONS PRINCIPALES =====

    /**
     * Product_Conditions: Produit
     */
    private function checkProductCondition(array $legacyData, ?array $packageData): bool
    {
        if (!$packageData) return false;

        return trim($legacyData['VISA_Product'] ?? '') === '' &&
               trim($packageData['VISA_BE_3'] ?? '') !== '';
    }

    /**
     * Inventory_Conditions: Qual_Logistique
     */
    private function checkInventoryCondition(array $legacyData, ?array $packageData): bool
    {
        if (!$packageData) return false;

        return trim($legacyData['VISA_Inventory'] ?? '') === '' &&
               trim($packageData['VISA_BE_3'] ?? '') !== '' &&
               trim($legacyData['VISA_GID'] ?? '') === '' &&
               ($legacyData['Doc_Type'] ?? '') !== 'DOC' &&
               ($legacyData['Inventory_Impact'] ?? '') !== 'NO IMPACT';
    }

    /**
     * Quality_Conditions: Quality
     */
    private function checkQualityCondition(array $legacyData, ?array $packageData): bool
    {
        if (!$packageData) return false;

        $docType = $legacyData['Doc_Type'] ?? '';
        return in_array($docType, ['PUR', 'ASSY', 'DOC']) &&
               trim($packageData['VISA_BE_3'] ?? '') !== '' &&
               trim($legacyData['VISA_Quality'] ?? '') === '';
    }

    /**
     * Project_Conditions: Project
     */
    private function checkProjectCondition(array $legacyData, ?array $packageData): bool
    {
        if (!$packageData) return false;

        $prodDraw = $legacyData['Prod_Draw'] ?? '';
        $project = $packageData['Project'] ?? 'STAND';

        return trim($legacyData['VISA_Project'] ?? '') === '' &&
               $project !== 'STAND' &&
               trim($packageData['VISA_BE_3'] ?? '') !== '' &&
               (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'));
    }

    // ===== CONDITIONS PRODUCTION =====

    /**
     * METRO_Conditions: Metro
     */
    private function checkMetroCondition(array $legacyData): bool
    {
        $condition1 = trim($legacyData['VISA_Prod'] ?? '') !== '' &&
                     ($legacyData['Proc_Type'] ?? '') === 'E';

        $condition2 = trim($legacyData['VISA_Quality'] ?? '') !== '' &&
                     ($legacyData['Doc_Type'] ?? '') === 'PUR';

        return ($condition1 || $condition2) &&
               trim($legacyData['VISA_Metro'] ?? '') === '';
    }

    /**
     * Q_PROD_Conditions: QProd
     */
    private function checkQProdCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        return trim($legacyData['VISA_Metro'] ?? '') !== '' &&
               in_array($docType, ['MACH', 'MOLD']) &&
               trim($legacyData['VISA_Q_PROD'] ?? '') === '';
    }

    /**
     * Prod_ASSY_Conditions: Assembly
     */
    private function checkAssemblyCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        $procType = $legacyData['Proc_Type'] ?? '';

        $condition1 = $docType === 'ASSY' && !str_starts_with($procType, 'F');
        $condition2 = $docType === 'DOC';

        return trim($legacyData['VISA_Product'] ?? '') !== '' &&
               ($condition1 || $condition2) &&
               trim($legacyData['VISA_Prod'] ?? '') === '';
    }

    /**
     * Method_Conditions: Methode_assemblage (CORRECTION avec Activity)
     */
    private function checkMethodCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        $materialType = $legacyData['Material_Type'] ?? '';

        // Récupérer l'Activity du package
        $packageData = $this->getPackageDataForConditions($legacyData);
        if (!$packageData) return false;

        // Récupérer l'Activity depuis la base legacy
        $activity = $this->getPackageActivity($legacyData);

        return trim($legacyData['VISA_Method'] ?? '') === '' &&
               (in_array($docType, ['ASSY', 'DOC']) || $materialType === 'PACKAGING') &&
               !str_starts_with($activity, 'MOB_IND') &&
               trim($legacyData['VISA_Prod'] ?? '') !== '';
    }

    /**
     * Récupérer l'Activity du package pour les conditions
     */
    private function getPackageActivity(array $legacyData): string
    {
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        if (!$packageNum) {
            return '';
        }

        try {
            $result = $this->oldDb->fetchAssociative(
                'SELECT Activity FROM tbl_released_package WHERE Rel_Pack_Num = ?',
                [$packageNum]
            );
            return $result['Activity'] ?? '';
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Prod_MACH_Conditions: Machining
     */
    private function checkMachiningCondition(array $legacyData): bool
    {
        return trim($legacyData['VISA_Product'] ?? '') !== '' &&
               ($legacyData['Doc_Type'] ?? '') === 'MACH' &&
               trim($legacyData['VISA_Prod'] ?? '') === '';
    }

    /**
     * Prod_MOLD_Conditions: Molding
     */
    private function checkMoldingCondition(array $legacyData): bool
    {
        return trim($legacyData['VISA_Product'] ?? '') !== '' &&
               ($legacyData['Doc_Type'] ?? '') === 'MOLD' &&
               trim($legacyData['VISA_Prod'] ?? '') === '';
    }

    // ===== CONDITIONS ACHAT =====

    /**
     * PUR_1_RFQ_Conditions: Achat_Rfq
     */
    private function checkAchatRfqCondition(array $legacyData): bool
    {
        $procType = $legacyData['Proc_Type'] ?? '';
        return trim($legacyData['VISA_PUR_1'] ?? '') === '' &&
               ($legacyData['Doc_Type'] ?? '') === 'PUR' &&
               (in_array($procType, ['', 'F', 'F30'])) &&
               trim($legacyData['VISA_Quality'] ?? '') !== '' &&
               trim($legacyData['VISA_Product'] ?? '') !== '';
    }

    /**
     * PUR_2_PRISDANS_Conditions: Achat_F30
     */
    private function checkAchatF30Condition(array $legacyData): bool
    {
        return trim($legacyData['VISA_PUR_2'] ?? '') === '' &&
               trim($legacyData['VISA_PUR_1'] ?? '') !== '' &&
               ($legacyData['Proc_Type'] ?? '') === 'F30';
    }

    /**
     * PUR_3_Conditions: Achat_FIA
     */
    private function checkAchatFIACondition(array $legacyData): bool
    {
        $procType = $legacyData['Proc_Type'] ?? '';
        return ($legacyData['VISA_PUR_1'] ?? '') !== '' &&
               trim($legacyData['VISA_PUR_3'] ?? '') === '' &&
               in_array($procType, ['F', 'F30']) &&
               trim($legacyData['VISA_GID_2'] ?? '') !== '';
    }

    /**
     * PUR_4_Conditions: Achat_RoHs_REACH
     */
    private function checkAchatRohsReachCondition(array $legacyData): bool
    {
        return trim($legacyData['VISA_PUR_4'] ?? '') === '' &&
               trim($legacyData['VISA_PUR_1'] ?? '') !== '';
    }

    /**
     * PUR_5_Conditions: Achat_Hts
     */
    private function checkAchatHtsCondition(array $legacyData): bool
    {
        return trim($legacyData['VISA_PUR_5'] ?? '') === '' &&
               trim($legacyData['VISA_PUR_3'] ?? '') !== '';
    }

    // ===== CONDITIONS AVANCÉES =====

    /**
     * Supply_Conditions: Planning
     */
    private function checkPlanningCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        return in_array($docType, ['ASSY', 'MACH', 'MOLD', 'DOC']) &&
               ($legacyData['VISA_Prod'] ?? '') !== '' &&
               ($legacyData['VISA_Supply'] ?? '') === '';
    }

    /**
     * MOF_Conditions: Indus
     */
    private function checkIndusCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        $condition1 = $docType === 'ASSY' && trim($legacyData['VISA_Metro'] ?? '') !== '';
        $condition2 = $docType === 'DOC';

        return ($condition1 || $condition2) &&
               trim($legacyData['VISA_Prod'] ?? '') !== '' &&
               trim($legacyData['VISA_MOF'] ?? '') === '';
    }

    /**
     * GID_1_Conditions: Core_Data (condition très complexe)
     */
    private function checkCoreDataCondition(array $legacyData, ?array $packageData): bool
    {
        if (!$packageData) return false;

        $docType = $legacyData['Doc_Type'] ?? '';
        $procType = $legacyData['Proc_Type'] ?? '';
        $project = $packageData['Project'] ?? 'STAND';
        $prodDraw = $legacyData['Prod_Draw'] ?? '';
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';

        // Condition principale selon le type de document
        $mainCondition = false;

        // Condition pour PUR F
        if ($docType === 'PUR' && $procType === 'F') {
            $mainCondition = trim($legacyData['VISA_PUR_1'] ?? '') !== '';
        }
        // Condition pour PUR F30
        elseif ($docType === 'PUR' && $procType === 'F30') {
            $mainCondition = trim($legacyData['VISA_PUR_2'] ?? '') !== '';
        }
        // Condition pour MACH/MOLD
        elseif (in_array($docType, ['MACH', 'MOLD'])) {
            $mainCondition = trim($legacyData['VISA_Metro'] ?? '') !== '' &&
                           trim($legacyData['VISA_Supply'] ?? '') !== '';
        }
        // Condition pour ASSY
        elseif ($docType === 'ASSY') {
            $projectCondition = $this->checkProjectConditionForCoreData($legacyData, $packageData);
            $mainCondition = trim($legacyData['VISA_Quality'] ?? '') !== '' &&
                           trim($legacyData['VISA_Metro'] ?? '') !== '' &&
                           trim($legacyData['VISA_Supply'] ?? '') !== '' &&
                           $projectCondition;
        }
        // Condition pour DOC
        elseif ($docType === 'DOC') {
            $projectCondition = $this->checkProjectConditionForCoreData($legacyData, $packageData);
            $mainCondition = trim($legacyData['VISA_Quality'] ?? '') !== '' &&
                           trim($legacyData['VISA_Metro'] ?? '') !== '' &&
                           $projectCondition;
        }

        // Condition d'inventaire
        $inventoryCondition = $this->checkInventoryConditionForCoreData($legacyData);

        return $mainCondition && $inventoryCondition && trim($legacyData['VISA_GID'] ?? '') === '';
    }

    /**
     * Vérifier la condition de projet pour Core_Data
     */
    private function checkProjectConditionForCoreData(array $legacyData, array $packageData): bool
    {
        $project = $packageData['Project'] ?? 'STAND';
        $prodDraw = $legacyData['Prod_Draw'] ?? '';
        $visaProject = $legacyData['VISA_Project'] ?? '';

        $condition1 = $project !== 'STAND' &&
                     trim($visaProject) !== '' &&
                     (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'));

        $condition2 = $project !== 'STAND' &&
                     trim($visaProject) === '' &&
                     (!str_starts_with($prodDraw, 'GA') && !str_starts_with($prodDraw, 'FT'));

        $condition3 = $project === 'STAND' && trim($visaProject) === '';

        return $condition1 || $condition2 || $condition3;
    }

    /**
     * Vérifier la condition d'inventaire pour Core_Data
     */
    private function checkInventoryConditionForCoreData(array $legacyData): bool
    {
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';
        $docType = $legacyData['Doc_Type'] ?? '';
        $visaInventory = $legacyData['VISA_Inventory'] ?? '';

        $condition1 = trim($visaInventory) !== '' &&
                     in_array($inventoryImpact, ['TO BE SCRAPPED', 'TO BE UPDATED']);

        $condition2 = trim($visaInventory) === '' &&
                     ($inventoryImpact === 'NO IMPACT' || $docType === 'DOC');

        return $condition1 || $condition2;
    }

    /**
     * GID_2_Conditions: Prod_Data
     */
    private function checkProdDataCondition(array $legacyData): bool
    {
        return trim($legacyData['VISA_GID'] ?? '') !== '' &&
               trim($legacyData['VISA_GID_2'] ?? '') === '';
    }

    /**
     * LABO_Conditions: methode_Labo
     */
    private function checkLaboCondition(array $legacyData): bool
    {
        return ($legacyData['Doc_Type'] ?? '') === 'ASSY' &&
               trim($legacyData['VISA_MOF'] ?? '') !== '' &&
               trim($legacyData['VISA_LABO'] ?? '') === '';
    }

    /**
     * ROUTING_ENTRY_Conditions: GID
     */
    private function checkGIDCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        $condition1 = $docType === 'ASSY' && trim($legacyData['VISA_MOF'] ?? '') !== '';
        $condition2 = in_array($docType, ['MOLD', 'MACH']) && trim($legacyData['VISA_Prod'] ?? '') !== '';

        return ($condition1 || $condition2) &&
               trim($legacyData['VISA_GID_2'] ?? '') !== '' &&
               trim($legacyData['VISA_ROUTING_ENTRY'] ?? '') === '';
    }

    /**
     * Finance_Conditions: Costing
     */
    private function checkCostingCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        $condition1 = trim($legacyData['VISA_PUR_3'] ?? '') !== '' && $docType === 'PUR';
        $condition2 = trim($legacyData['VISA_ROUTING_ENTRY'] ?? '') !== '' && $docType !== 'PUR';

        return $docType !== 'DOC' &&
               trim($legacyData['VISA_Finance'] ?? '') === '' &&
               ($condition1 || $condition2);
    }

    /**
     * Créer les timestamps basés sur tous les visas
     */
    private function createStateTimestampsFromAllVisas(Document $doc, array $legacyData): void
    {
        $stateTimestamps = [];
        $updates = [];

        // Récupérer les données du package pour les dates BE
        $packageData = $this->getPackageDataForConditions($legacyData);

        // Mapping des visas vers les états et leurs dates
        $visaToStateMapping = [
            // Visas du package
            'Creation_VISA' => ['state' => 'BE_0', 'date_field' => 'Creation_Date'],
            'VISA_BE_2' => ['state' => 'BE_1', 'date_field' => 'DATE_BE_2'],
            'VISA_BE_3' => ['state' => 'BE', 'date_field' => 'DATE_BE_3'],

            // Visas du document
            'VISA_Product' => ['state' => 'Produit', 'date_field' => 'DATE_Product'],
            'VISA_Project' => ['state' => 'Project', 'date_field' => 'DATE_Project'],
            'VISA_Quality' => ['state' => 'Quality', 'date_field' => 'DATE_Quality'],
            'VISA_Inventory' => ['state' => 'Qual_Logistique', 'date_field' => 'DATE_Inventory'],
            'VISA_Prod' => ['state' => 'Assembly', 'date_field' => 'DATE_Prod'], // Générique
            'VISA_Method' => ['state' => 'Methode_assemblage', 'date_field' => 'DATE_Method'],
            'VISA_Supply' => ['state' => 'Planning', 'date_field' => 'DATE_Supply'],
            'VISA_Metro' => ['state' => 'Metro', 'date_field' => 'DATE_Metro'],
            'VISA_MOF' => ['state' => 'Indus', 'date_field' => 'DATE_MOF'],
            'VISA_PUR_1' => ['state' => 'Achat_Rfq', 'date_field' => 'DATE_PUR_1'],
            'VISA_PUR_2' => ['state' => 'Achat_F30', 'date_field' => 'DATE_PUR_2'],
            'VISA_PUR_3' => ['state' => 'Achat_FIA', 'date_field' => 'DATE_PUR_3'],
            'VISA_PUR_4' => ['state' => 'Achat_RoHs_REACH', 'date_field' => 'DATE_PUR_4'],
            'VISA_PUR_5' => ['state' => 'Achat_Hts', 'date_field' => 'DATE_PUR_5'],
            'VISA_GID' => ['state' => 'Core_Data', 'date_field' => 'DATE_GID'],
            'VISA_GID_2' => ['state' => 'Prod_Data', 'date_field' => 'DATE_GID_2'],
            'VISA_ROUTING_ENTRY' => ['state' => 'GID', 'date_field' => 'DATE_ROUTING_ENTRY'],
            'VISA_Finance' => ['state' => 'Costing', 'date_field' => 'DATE_Finance'],
            'VISA_LABO' => ['state' => 'methode_Labo', 'date_field' => 'DATE_LABO'],
            'VISA_Q_PROD' => ['state' => 'QProd', 'date_field' => 'DATE_Q_PROD'],
        ];

        // Créer les timestamps pour chaque visa présent
        foreach ($visaToStateMapping as $visaField => $mapping) {
            $state = $mapping['state'];
            $dateField = $mapping['date_field'];

            // Vérifier si le visa existe
            $hasVisa = false;
            $visaDate = null;

            // Pour les visas du package
            if (in_array($visaField, ['Creation_VISA', 'VISA_BE_2', 'VISA_BE_3']) && $packageData) {
                $hasVisa = !empty(trim($packageData[$visaField] ?? ''));
                if ($hasVisa) {
                    if ($visaField === 'Creation_VISA') {
                        $visaDate = $packageData['Creation_Date'] ?? $legacyData['Reservation_Date'] ?? null;
                    } else {
                        $visaDate = $packageData[$dateField] ?? null;
                    }
                }
            }
            // Pour les visas du document
            else {
                $hasVisa = !empty(trim($legacyData[$visaField] ?? ''));
                if ($hasVisa) {
                    $visaDate = $legacyData[$dateField] ?? null;
                }
            }

            // Si le visa existe et qu'on a une date valide
            if ($hasVisa && $visaDate && $this->isValidDate($visaDate)) {
                $stateTimestamps[$state] = [[
                    'enter' => $visaDate,
                    'exit' => null,
                    'from_state' => null
                ]];

                $updates[] = [
                    'type' => 'visa',
                    'date' => $visaDate,
                    'user_id' => $this->adminUser ? $this->adminUser->getId() : null,
                    'user_name' => $this->adminUser ? $this->adminUser->getPrenom() . ' ' . $this->adminUser->getNom() : 'Migration',
                    'details' => "Migration du visa $visaField"
                ];
            }
        }

        // Ajouter une entrée de création si pas déjà présente
        if (!isset($stateTimestamps['BE_0'])) {
            $creationDate = $legacyData['Creation_Date'] ?? $legacyData['Reservation_Date'] ?? date('Y-m-d H:i:s');
            $stateTimestamps['BE_0'] = [[
                'enter' => $creationDate,
                'exit' => null,
                'from_state' => null
            ]];

            $updates[] = [
                'type' => 'creation',
                'date' => $creationDate,
                'user_id' => $this->adminUser ? $this->adminUser->getId() : null,
                'user_name' => $this->adminUser ? $this->adminUser->getPrenom() . ' ' . $this->adminUser->getNom() : 'Migration',
                'details' => 'Document créé lors de la migration'
            ];
        }

        // Appliquer les timestamps et updates
        $doc->setStateTimestamps($stateTimestamps);
        $doc->setUpdates($updates);
    }

    /**
     * Créer les visas legacy dans l'ordre chronologique et appliquer le workflow après chaque visa
     */
    private function createLegacyVisasSequentially(Document $doc, array $legacyData, OutputInterface $output): void
    {
        // Mapping correct des noms de visas (basé sur VisaChecker.php)
        $visaMapping = [
            'Product' => 'visa_produit',
            'Project' => 'visa_Project',
            'Quality' => 'visa_Quality',
            'Inventory' => 'visa_Logistique', // Legacy Inventory maps to Logistique
            'Prod' => 'visa_prod',
            'Method' => 'visa_Methode_assemblage',
            'Supply' => 'visa_Planning', // Legacy Supply maps to Planning
            'Metro' => 'visa_Metro',
            'PUR_1' => 'visa_Achat_Rfq', // Legacy PUR_1 maps to Achat_Rfq
            'PUR_2' => 'visa_Achat_F30', // Legacy PUR_2 maps to Achat_F30
            'PUR_3' => 'visa_Achat_FIA', // Legacy PUR_3 maps to Achat_FIA
            'PUR_5' => 'visa_Achat_Hts', // Legacy PUR_5 maps to Achat_Hts
            'GID' => 'visa_GID',
            'GID_2' => 'visa_Core_Data', // Legacy GID_2 maps to Core_Data
            'ROUTING_ENTRY' => 'visa_Prod_Data', // Legacy ROUTING_ENTRY maps to Prod_Data
            'Finance' => 'visa_Costing', // Legacy Finance maps to Costing
        ];

        // Collecter tous les visas avec leurs dates pour les trier chronologiquement
        $visasWithDates = [];
        foreach ($visaMapping as $visaKey => $visaName) {
            $visaCol = "VISA_{$visaKey}";
            $dateCol = "DATE_{$visaKey}";

            if (isset($legacyData[$visaCol]) && !empty($legacyData[$visaCol]) &&
                isset($legacyData[$dateCol]) && $this->isValidDate($legacyData[$dateCol])) {

                $visasWithDates[] = [
                    'name' => $visaName,
                    'date' => $legacyData[$dateCol],
                    'legacy_key' => $visaKey
                ];
            }
        }

        // Trier par date chronologique
        usort($visasWithDates, function($a, $b) {
            return strtotime($a['date']) <=> strtotime($b['date']);
        });

        // Créer chaque visa et appliquer le workflow
        foreach ($visasWithDates as $visaData) {
            $this->createSingleVisaAndApplyWorkflow($doc, $visaData, $output);
        }
    }

    /**
     * Créer un visa unique et appliquer le workflow immédiatement
     */
    private function createSingleVisaAndApplyWorkflow(Document $doc, array $visaData, OutputInterface $output): void
    {
        // Créer le visa
        $visa = new Visa();
        $visa->setName($visaData['name']);
        $visa->setDateVisa(\DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $visaData['date']) ?: new \DateTimeImmutable($visaData['date']));
        $visa->setStatus('valid');
        $visa->setReleasedDrawing($doc);

        if ($this->adminUser) {
            $visa->setValidator($this->adminUser);
        }

        $this->em->persist($visa);
        $this->em->flush(); // Flush immédiatement pour que le visa soit disponible

        // Rafraîchir le document pour que les visas soient disponibles
        $this->em->refresh($doc);

        $output->writeln("<info>  → Visa créé: {$visaData['name']} ({$visaData['date']})</info>");

        // Appliquer le workflow immédiatement après la création du visa
        $this->applyWorkflowForMigration($doc, $visaData['date'], $output);
    }

    /**
     * Migrer les visas du package de manière séquentielle
     */
    private function migratePackageVisasSequentially(Document $doc, array $legacyData, OutputInterface $output): void
    {
        // Récupérer les visas du package dans l'ordre chronologique
        $packageVisas = $this->getPackageVisasInOrder($legacyData);

        if (empty($packageVisas)) {
            $output->writeln("<comment>  → Aucun visa de package trouvé pour {$legacyData['Reference']}</comment>");
            return;
        }

        $output->writeln("<info>  → Visas de package trouvés: " . implode(', ', array_column($packageVisas, 'name')) . "</info>");

        foreach ($packageVisas as $visaData) {
            $this->createSingleVisaAndApplyWorkflow($doc, $visaData, $output);
        }
    }

    /**
     * Récupérer les visas du package dans l'ordre chronologique
     */
    private function getPackageVisasInOrder(array $legacyData): array
    {
        $packageVisas = [];

        // Récupérer les données du package depuis la base legacy
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        if (!$packageNum) {
            return $packageVisas; // Pas de package
        }

        try {
            // Récupérer les visas du package legacy
            $packageData = $this->oldDb->fetchAssociative(
                'SELECT Creation_VISA, VISA_BE_2, VISA_BE_3, Creation_Date, Reservation_Date, DATE_BE_2, DATE_BE_3 FROM tbl_released_package WHERE Rel_Pack_Num = ?',
                [$packageNum]
            );

            if (!$packageData) {
                return $packageVisas; // Package non trouvé
            }
        } catch (\Exception $e) {
            return $packageVisas; // Erreur lors de la récupération
        }

        // Vérifier les visas du package dans l'ordre chronologique (NOMS CORRECTS avec majuscules)
        $packageVisaMapping = [
            'Creation_VISA' => 'visa_BE_0',  // Correction: majuscules
            'VISA_BE_2' => 'visa_BE_1',      // Correction: majuscules
            'VISA_BE_3' => 'visa_BE'         // Correction: majuscules
        ];

        foreach ($packageVisaMapping as $legacyVisa => $newVisaName) {
            if (isset($packageData[$legacyVisa]) && !empty($packageData[$legacyVisa])) {
                // Utiliser la date de création pour Creation_VISA, sinon chercher la date correspondante
                $date = null;
                if ($legacyVisa === 'Creation_VISA') {
                    $date = $packageData['Creation_Date'] ?? $packageData['Reservation_Date'] ?? date('Y-m-d H:i:s');
                } else {
                    $dateCol = str_replace('VISA_', 'DATE_', $legacyVisa);
                    if (isset($packageData[$dateCol]) && $this->isValidDate($packageData[$dateCol])) {
                        $date = $packageData[$dateCol];
                    }
                }

                if ($date) {
                    $packageVisas[] = [
                        'name' => $newVisaName,
                        'date' => $date,
                        'legacy_key' => $legacyVisa
                    ];
                }
            }
        }

        // Trier par date chronologique
        usort($packageVisas, function($a, $b) {
            return strtotime($a['date']) <=> strtotime($b['date']);
        });

        return $packageVisas;
    }

    /**
     * Version adaptée de workflowMove pour la migration
     * Applique toutes les transitions possibles jusqu'à stabilisation
     */
    private function applyWorkflowForMigration(Document $doc, string $visaDate, OutputInterface $output): void
    {
        $workflow = $this->workflowRegistry->get($doc, 'document_workflow');
        $maxIterations = 10; // Éviter les boucles infinies
        $iteration = 0;

        do {
            $iteration++;
            $markingStart = $workflow->getMarking($doc)->getPlaces();
            $enabledTransitions = $workflow->getEnabledTransitions($doc);
            $hasChanges = false;

            // Debug: afficher les états actuels et transitions disponibles
            $currentStates = array_keys($markingStart);
            $transitionNames = array_map(fn($t) => $t->getName(), $enabledTransitions);
            $output->writeln("<comment>    Itération $iteration - États actuels: " . implode(', ', $currentStates) . "</comment>");
            $output->writeln("<comment>    Transitions disponibles: " . implode(', ', $transitionNames) . "</comment>");

            if (empty($enabledTransitions)) {
                $output->writeln("<comment>    Aucune transition disponible - stabilisé</comment>");
                break;
            }

            // Récupérer les visas du document pour vérifier s'il a déjà terminé certains états
            $documentVisas = $doc->getVisasArray();
            $marking = [];

            foreach ($enabledTransitions as $transition) {
                $to = $transition->getTos()[0];

                // Vérifier si le document a déjà le visa correspondant à cet état
                // Si oui, il ne devrait pas être placé dans cet état car il l'a déjà terminé
                $stateVisa = $this->getVisaNameForState($to);
                if ($stateVisa && in_array($stateVisa, $documentVisas)) {
                    $output->writeln("<comment>    État $to ignoré car visa $stateVisa déjà présent</comment>");
                    continue; // Ignorer cette transition car l'état est déjà terminé
                }

                // On marque la place « to » comme atteignable
                $marking[$to] = 1;
                $hasChanges = true;

                // Enregistrer l'entrée dans le nouvel état avec la date du visa
                $this->addStateEnterWithVisaDate($doc, $to, $visaDate);

                // On supprime la place « from » de la liste des places courantes
                // si elle y est encore présente
                $from = $transition->getFroms()[0];
                if (isset($markingStart[$from])) {
                    // Enregistrer la sortie de l'état précédent
                    $this->addStateExitWithVisaDate($doc, $from, $visaDate);
                    unset($markingStart[$from]);
                }

                // Appliquer les règles d'automatisation comme dans workflowMove
                $this->applyAutomationRules($doc, $to);
            }

            if ($hasChanges) {
                // Fusion : on conserve aussi les places non « consommées »
                $finalMarking = array_merge($marking, $markingStart);

                // Mise à jour de l'entité Document
                $doc->setCurrentSteps($finalMarking);

                // Flush pour sauvegarder les changements
                $this->em->flush();
                $this->em->refresh($doc); // Rafraîchir pour la prochaine itération
            }

        } while ($hasChanges && $iteration < $maxIterations);

        $finalStates = array_keys($workflow->getMarking($doc)->getPlaces());
        $output->writeln("<info>  → États finaux après $iteration itération(s): " . implode(', ', $finalStates) . "</info>");
    }

    /**
     * Récupérer le nom du visa correspondant à un état
     */
    private function getVisaNameForState(string $state): ?string
    {
        // Mapping des états vers les noms de visas correspondants
        $stateToVisaMapping = [
            'BE_0' => 'visa_BE_0',
            'BE_1' => 'visa_BE_1',
            'BE' => 'visa_BE',
            'Produit' => 'visa_produit',
            'Project' => 'visa_Project',
            'Quality' => 'visa_Quality',
            'Qual_Logistique' => 'visa_Qual_Logistique',
            'Logistique' => 'visa_Logistique',
            'Assembly' => 'visa_prod',
            'Machining' => 'visa_prod',
            'Molding' => 'visa_prod',
            'Methode_assemblage' => 'visa_Methode_assemblage',
            'Planning' => 'visa_Planning',
            'Metro' => 'visa_Metro',
            'Indus' => 'visa_Indus',
            'methode_Labo' => 'visa_methode_Labo',
            'QProd' => 'visa_QProd',
            'Core_Data' => 'visa_Core_Data',
            'Prod_Data' => 'visa_Prod_Data',
            'GID' => 'visa_GID',
            'Costing' => 'visa_Costing',
            'Achat_Rfq' => 'visa_Achat_Rfq',
            'Achat_F30' => 'visa_Achat_F30',
            'Achat_FIA' => 'visa_Achat_FIA',
            'Achat_Hts' => 'visa_Achat_Hts',
            'Saisie_hts' => 'visa_SaisieHts',
            'Tirage_Plans' => 'visa_Tirage_Plans',
        ];

        return $stateToVisaMapping[$state] ?? null;
    }

    /**
     * Ajouter une entrée d'état avec la date du visa
     */
    private function addStateEnterWithVisaDate(Document $doc, string $state, string $visaDate): void
    {
        // Utiliser la méthode normale mais modifier le timestamp après
        $doc->addStateEnter($state, $this->adminUser);

        // Modifier le timestamp pour utiliser la vraie date du visa
        $this->updateStateTimestampWithVisaDate($doc, $state, $visaDate);
    }

    /**
     * Ajouter une sortie d'état avec la date du visa
     */
    private function addStateExitWithVisaDate(Document $doc, string $state, string $visaDate): void
    {
        // Pour la sortie, on utilise la date du visa
        $doc->addStateExit($state);
        // Note: La date de sortie sera automatiquement gérée par addStateExit
    }

    /**
     * Appliquer les règles d'automatisation comme dans workflowMove
     */
    private function applyAutomationRules(Document $doc, string $to): void
    {
        // Si le document arrive dans la place Assembly, on modifie son procType à E et son Unit à PC
        if ($to === 'Assembly') {
            $doc->setProcType('E');
            $doc->setUnit('PC');
            $doc->addUpdate('edit', $this->adminUser, 'Modification automatique pour Assembly: procType=E, Unit=PC');
        }

        // Si le document arrive dans la place Machining
        if ($to === 'Machining') {
            $doc->setProcType('E');
            $doc->setUnit('PC');
            $doc->setProdAgent('USI');
            $doc->setMaterialType('HALB');
            $doc->setMatProdType('HALB');
            $doc->addUpdate('edit', $this->adminUser, 'Modification automatique pour Machining: procType=E, Unit=PC, prodAgent=USI, materialType=HALB, matProdType=HALB');
        }

        // Si le document arrive dans la place Molding
        if ($to === 'Molding') {
            $doc->setProcType('E');
            $doc->setMaterialType('HALB');
            $doc->setMatProdType('HALB');
            $doc->addUpdate('edit', $this->adminUser, 'Modification automatique pour Molding: procType=E, materialType=HALB, matProdType=HALB');
        }
    }

    /**
     * Mettre à jour le timestamp d'un état avec la date du visa
     */
    private function updateStateTimestampWithVisaDate(Document $doc, string $state, string $visaDate): void
    {
        $stateTimestamps = $doc->getRawStateTimestamps() ?? [];

        if (isset($stateTimestamps[$state]) && !empty($stateTimestamps[$state])) {
            // Mettre à jour la dernière entrée avec la date du visa
            $lastIndex = count($stateTimestamps[$state]) - 1;
            $stateTimestamps[$state][$lastIndex]['enter'] = $visaDate;

            $doc->setStateTimestamps($stateTimestamps);
        }
    }

















    private function applyPackageVisasToDocuments(array $packageVisaData, array $packageMapping, OutputInterface $output): void
    {
        // Appliquer les visas des packages à tous leurs documents
        // 1. No visas in legacy → No visas in new system
        // 2. Has Creation_VISA only → Add visa_be_0 to all documents
        // 3. Has Creation_VISA + VISA_BE_2 → Add visa_be_0 + visa_be_1 to all documents
        // 4. Has Creation_VISA + VISA_BE_2 + VISA_BE_3 → Add visa_be_0 + visa_be_1 + visa_be to all documents

        $processedPackages = 0;
        $processedDocuments = 0;

        foreach ($packageVisaData as $packageNum => $legacyData) {
            // Récupérer les documents qui appartiennent à ce package legacy
            // Utiliser une requête SQL directe pour trouver les documents
            $sql = 'SELECT d.id FROM document d
                    JOIN released_package p ON d.rel_pack_id = p.id
                    WHERE p.id = (SELECT id FROM released_package WHERE id = ? LIMIT 1)';

            try {
                $documentData = $this->em->getConnection()->fetchAllAssociative($sql, [$packageNum]);

                if (empty($documentData)) {
                    $output->writeln("<comment>Package $packageNum → Aucun document trouvé</comment>");
                    continue;
                }

                // Récupérer les entités Document
                $documentIds = array_column($documentData, 'id');
                $documents = $this->em->getRepository(Document::class)->findBy(['id' => $documentIds]);

                if (empty($documents)) {
                    $output->writeln("<comment>Package $packageNum → Aucun document</comment>");
                    continue;
                }
            } catch (\Exception $e) {
                $output->writeln("<error>Erreur lors de la récupération des documents pour le package $packageNum: " . $e->getMessage() . "</error>");
                continue;
            }

            // Vérifier les visas legacy disponibles
            $hasCreationVisa = !empty($legacyData['Creation_VISA']);
            $hasVisaBE2 = !empty($legacyData['VISA_BE_2']);
            $hasVisaBE3 = !empty($legacyData['VISA_BE_3']);

            $visasToApply = [];

            // Déterminer quels visas appliquer selon la progression legacy
            if ($hasCreationVisa || $hasVisaBE2 || $hasVisaBE3) {
                // Toujours commencer par visa_be_0
                $visasToApply[] = [
                    'name' => 'visa_be_0',
                    'date' => $legacyData['Creation_Date'] ?? $legacyData['Reservation_Date'] ?? null
                ];

                if ($hasVisaBE2 || $hasVisaBE3) {
                    $visasToApply[] = [
                        'name' => 'visa_be_1',
                        'date' => $legacyData['DATE_BE_2'] ?? null
                    ];
                }

                if ($hasVisaBE3) {
                    $visasToApply[] = [
                        'name' => 'visa_be',
                        'date' => $legacyData['DATE_BE_3'] ?? null
                    ];
                }
            }

            // Appliquer les visas à tous les documents du package
            if (!empty($visasToApply)) {
                foreach ($documents as $document) {
                    foreach ($visasToApply as $visaData) {
                        $this->createDocumentVisa($document, $visaData['name'], $visaData['date']);
                    }
                    $processedDocuments++;
                }

                $visaNames = array_column($visasToApply, 'name');
                $output->writeln("<comment>Package $packageNum → " . implode(' + ', $visaNames) . " appliqués à " . count($documents) . " documents</comment>");
            } else {
                $output->writeln("<comment>Package $packageNum → Aucun visa (état initial)</comment>");
            }

            $processedPackages++;

            // Flush périodique
            if ($processedPackages % 50 === 0) {
                $this->em->flush();
                $output->writeln("<info>Flush visas packages: $processedPackages packages, $processedDocuments documents traités</info>");
            }
        }

        $output->writeln("<info>Visas de packages appliqués: $processedPackages packages, $processedDocuments documents traités</info>");
    }

    private function createDocumentVisa(Document $document, string $visaName, ?string $dateString): void
    {
        // Vérifier si le visa existe déjà pour éviter les doublons
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === $visaName) {
                return; // Visa déjà présent
            }
        }

        $visa = new \App\Entity\Visa();
        $visa->setName($visaName);
        $visa->setStatus('valid');
        $visa->setReleasedDrawing($document);

        // Définir la date du visa
        if (!empty($dateString) && $this->isValidDate($dateString)) {
            $visa->setDateVisa(\DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $dateString) ?: new \DateTimeImmutable($dateString));
        } else {
            // Fallback sur maintenant
            $visa->setDateVisa(new \DateTimeImmutable());
        }

        // Ajouter un validator (utiliser Admin en fallback)
        if ($this->adminUser) {
            $visa->setValidator($this->adminUser);
        }

        $this->em->persist($visa);
    }

    private $packageVisaCache = []; // Cache pour éviter les requêtes répétées

    private function migratePackageVisasForDocument(Document $document, array $legacyData, OutputInterface $output): void
    {
        // Récupérer le numéro de package legacy
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        if (!$packageNum) {
            return; // Pas de package, rien à faire
        }

        // Vérifier le cache d'abord
        if (!isset($this->packageVisaCache[$packageNum])) {
            try {
                // Récupérer les visas du package legacy
                $packageData = $this->oldDb->fetchAssociative(
                    'SELECT Creation_VISA, VISA_BE_2, VISA_BE_3, Creation_Date, DATE_BE_2, DATE_BE_3 FROM tbl_released_package WHERE Rel_Pack_Num = ?',
                    [$packageNum]
                );

                if (!$packageData) {
                    $this->packageVisaCache[$packageNum] = null; // Marquer comme non trouvé
                    return;
                }

                $this->packageVisaCache[$packageNum] = $packageData;
            } catch (\Exception $e) {
                $reference = $legacyData['Reference'] ?? 'Unknown';
                $output->writeln("<error>Erreur lors de la récupération du package $packageNum pour $reference: " . $e->getMessage() . "</error>");
                return;
            }
        }

        $packageData = $this->packageVisaCache[$packageNum];
        if (!$packageData) {
            return; // Package non trouvé (depuis le cache)
        }

            // Appliquer les visas du package selon la progression legacy
            $hasCreationVisa = !empty($packageData['Creation_VISA']);
            $hasVisaBE2 = !empty($packageData['VISA_BE_2']);
            $hasVisaBE3 = !empty($packageData['VISA_BE_3']);

            // Créer les visas BE selon la progression du package (noms corrects)
            if ($hasCreationVisa) {
                // Toujours commencer par visa_BE_0
                $this->createDocumentVisaIfNotExists($document, 'visa_BE_0', $packageData['Creation_Date'] ?? null);

                if ($hasCreationVisa & $hasVisaBE2 ) {
                    $this->createDocumentVisaIfNotExists($document, 'visa_BE_1', $packageData['DATE_BE_2'] ?? null);
                }

                if ($hasCreationVisa && $hasVisaBE2 && $hasVisaBE3) {
                    $this->createDocumentVisaIfNotExists($document, 'visa_BE', $packageData['DATE_BE_3'] ?? null);
                }

                $reference = $legacyData['Reference'] ?? 'Unknown';
                $visaCount = ($hasCreationVisa ? 1 : 0) + ($hasVisaBE2 ? 1 : 0) + ($hasVisaBE3 ? 1 : 0);
                $output->writeln("<comment>Document $reference → $visaCount visa(s) de package appliqué(s)</comment>");
            }
    }

    private function createDocumentVisaIfNotExists(Document $document, string $visaName, ?string $dateString): void
    {
        // Vérifier si le visa existe déjà pour éviter les doublons
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === $visaName) {
                return; // Visa déjà présent
            }
        }

        $visa = new \App\Entity\Visa();
        $visa->setName($visaName);
        $visa->setStatus('valid');
        $visa->setReleasedDrawing($document);

        // Définir la date du visa
        if (!empty($dateString) && $this->isValidDate($dateString)) {
            $visa->setDateVisa(\DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $dateString) ?: new \DateTimeImmutable($dateString));
        } else {
            // Fallback sur maintenant
            $visa->setDateVisa(new \DateTimeImmutable());
        }

        // Ajouter un validator (utiliser Admin en fallback)
        if ($this->adminUser) {
            $visa->setValidator($this->adminUser);
        }

        $this->em->persist($visa);
    }

    private function normalizeName(string $s): string
{
    // 1) trim + retirer tout ce qui n'est pas lettre/chiffre/espace
    $s = trim($s);
    $s = preg_replace('/[^\p{L}\p{N}\s]/u', '', $s);

    // 2) décomposer les accents (NFD) et supprimer les marques
    if (class_exists(\Normalizer::class)) {
        $s = \Normalizer::normalize($s, \Normalizer::FORM_D);
        $s = preg_replace('/\p{M}/u', '', $s);
    }

    // 3) retourner en majuscules
    return mb_strtoupper($s, 'UTF-8');
}

    // ===== MÉTHODES OPTIMISÉES =====

    /**
     * Initialiser tous les caches pour optimiser les performances
     */
    private function initializeAllCaches(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation de tous les caches...</info>');

        // 1. Cache des utilisateurs avec requête SQL optimisée
        $this->initializeUserCacheOptimized($output);

        // 2. Cache des projets
        $this->initializeProjectCache($output);

        // 3. Cache des données de packages
        $this->initializePackageDataCache($output);

        $output->writeln('<info>Tous les caches initialisés.</info>');
    }

    /**
     * Initialiser le cache des utilisateurs avec SQL optimisé
     */
    private function initializeUserCacheOptimized(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des utilisateurs optimisé...</info>');

        // Vider les caches
        $this->userCache = [];
        $this->userIdCache = [];

        // Charger l'utilisateur Admin
        $this->adminUser = $this->users->findOneBy(['nom' => 'Admin']);
        if ($this->adminUser) {
            $output->writeln('<info>Utilisateur Admin trouvé pour fallback.</info>');
        } else {
            $output->writeln('<e>Aucun utilisateur Admin trouvé !</e>');
        }

        // Charger tous les utilisateurs en une seule requête SQL
        $sql = 'SELECT id, nom, prenom FROM user ORDER BY nom, prenom';
        $users = $this->em->getConnection()->fetchAllAssociative($sql);

        foreach ($users as $userData) {
            $normalizedNom = $this->normalizeName($userData['nom']);
            $normalizedPrenom = $this->normalizeName($userData['prenom'] ?? '');

            // Créer plusieurs clés de cache pour différents formats
            $keys = [
                $normalizedNom,
                $userData['nom'],
                $normalizedNom . ' ' . $normalizedPrenom,
                $userData['nom'] . ' ' . $userData['prenom']
            ];

            foreach ($keys as $key) {
                if (!empty($key)) {
                    $this->userIdCache[trim($key)] = $userData['id'];
                }
            }
        }

        $output->writeln('<info>Cache utilisateurs initialisé avec ' . count($users) . ' utilisateurs.</info>');
    }

    /**
     * Initialiser le cache des projets
     */
    private function initializeProjectCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des projets...</info>');

        $sql = 'SELECT id, otp FROM project';
        $projects = $this->em->getConnection()->fetchAllAssociative($sql);

        foreach ($projects as $project) {
            $this->projectIdCache[$project['otp']] = $project['id'];
        }

        $output->writeln('<info>Cache projets initialisé avec ' . count($projects) . ' projets.</info>');
    }

    /**
     * Initialiser le cache des données de packages
     */
    private function initializePackageDataCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des données de packages...</info>');

        $sql = 'SELECT Rel_Pack_Num, Creation_VISA, VISA_BE_2, VISA_BE_3, Project, Activity,
                       Creation_Date, Reservation_Date, DATE_BE_2, DATE_BE_3
                FROM tbl_released_package';
        $packages = $this->oldDb->fetchAllAssociative($sql);

        foreach ($packages as $package) {
            $this->packageDataCache[$package['Rel_Pack_Num']] = $package;
        }

        $output->writeln('<info>Cache packages initialisé avec ' . count($packages) . ' packages.</info>');
    }

    /**
     * Créer les projets manquants avec SQL optimisé
     */
    private function createMissingProjectsOptimized(OutputInterface $output): void
    {
        $output->writeln('<info>Création des projets manquants (optimisé)...</info>');

        // Récupérer tous les codes de projets uniques de l'ancienne base
        $projectCodes = $this->oldDb->fetchAllAssociative(
            'SELECT DISTINCT Project FROM tbl_released_package WHERE Project IS NOT NULL'
        );

        $toCreate = [];
        foreach ($projectCodes as $row) {
            $otpCode = $row['Project'];
            if (!isset($this->projectIdCache[$otpCode])) {
                $toCreate[] = $otpCode;
            }
        }

        if (empty($toCreate)) {
            $output->writeln('<info>Aucun projet à créer.</info>');
            return;
        }

        // Créer les projets en batch avec SQL brut
        $values = [];
        $params = [];
        $paramIndex = 1;

        foreach ($toCreate as $otpCode) {
            $values[] = "(?, ?, 'active')";
            $params[] = $otpCode;
            $params[] = "Projet migré: " . $otpCode;
        }

        $sql = 'INSERT INTO project (otp, title, status) VALUES ' . implode(', ', $values);
        $this->em->getConnection()->executeStatement($sql, $params);

        // Mettre à jour le cache
        $newProjects = $this->em->getConnection()->fetchAllAssociative(
            'SELECT id, otp FROM project WHERE otp IN (' . implode(',', array_fill(0, count($toCreate), '?')) . ')',
            $toCreate
        );

        foreach ($newProjects as $project) {
            $this->projectIdCache[$project['otp']] = $project['id'];
        }

        $output->writeln('<info>' . count($toCreate) . ' projets créés avec succès.</info>');
    }

    /**
     * Migrer les packages avec SQL brut optimisé
     */
    private function migratePackagesOptimized(OutputInterface $output): array
    {
        $output->writeln('<info>Migration des packages (optimisé)...</info>');

        // Récupérer tous les packages legacy
        $sql = 'SELECT * FROM tbl_released_package';
        $packages = $this->oldDb->fetchAllAssociative($sql);

        $packageMapping = [];
        $validPackages = [];

        // Préparer les données pour l'insertion en batch
        foreach ($packages as $package) {
            $ownerId = $this->findUserIdOptimized($package['Rel_Pack_Owner']);
            $verifId = $this->findUserIdOptimized($package['Verif_Req_Owner']);
            $validId = $this->findUserIdOptimized($package['BE_3_Req_Owner']);
            $projectId = $this->projectIdCache[$package['Project']] ?? null;

            if (!$ownerId || !$projectId) {
                $output->writeln(sprintf(
                    '<e>Package %s ignoré: owner=%s, project=%s</e>',
                    $package['Rel_Pack_Num'],
                    $package['Rel_Pack_Owner'] ?? 'null',
                    $package['Project'] ?? 'null'
                ));
                continue;
            }

            $validPackages[] = [
                'legacy_num' => $package['Rel_Pack_Num'],
                'owner_id' => $ownerId,
                'verif_id' => $verifId,
                'valid_id' => $validId,
                'project_id' => $projectId,
                'description' => $package['Observations'] ?? '',
                'activity' => $package['Activity'] ?? '',
                'ex' => $package['Ex'] ?? '',
                'reservation_date' => $this->formatDateForSql($package['Reservation_Date'] ?? null),
                'creation_date' => $this->formatDateForSql($package['Creation_Date'] ?? null)
            ];
        }

        if (empty($validPackages)) {
            $output->writeln('<e>Aucun package valide à migrer!</e>');
            return [];
        }

        // Insertion en batch avec SQL brut
        $this->insertPackagesBatch($validPackages, $output);

        // Récupérer les IDs générés pour le mapping
        $packageMapping = $this->buildPackageMapping($validPackages, $output);

        $output->writeln('<info>' . count($packageMapping) . ' packages migrés avec succès.</info>');
        return $packageMapping;
    }

    /**
     * Trouver l'ID d'un utilisateur de manière optimisée
     */
    private function findUserIdOptimized(?string $raw): ?int
    {
        if (empty($raw)) {
            return null;
        }

        $cacheKey = trim($raw);

        // Vérifier le cache direct
        if (isset($this->userIdCache[$cacheKey])) {
            return $this->userIdCache[$cacheKey];
        }

        // Essayer avec normalisation
        $normalized = $this->normalizeName($raw);
        if (isset($this->userIdCache[$normalized])) {
            return $this->userIdCache[$normalized];
        }

        // Fallback vers Admin si disponible
        return $this->adminUser ? $this->adminUser->getId() : null;
    }

    /**
     * Formater une date pour SQL
     */
    private function formatDateForSql(?string $date): ?string
    {
        if (empty($date) || !$this->isValidDate($date)) {
            return null;
        }

        try {
            $dateTime = new \DateTime($date);
            return $dateTime->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Insérer les packages en batch
     */
    private function insertPackagesBatch(array $packages, OutputInterface $output): void
    {
        $batchSize = 100;
        $batches = array_chunk($packages, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $package) {
                $values[] = '(?, ?, ?, ?, ?, ?, ?, ?, ?)';
                $params = array_merge($params, [
                    $package['owner_id'],
                    $package['verif_id'],
                    $package['valid_id'],
                    $package['project_id'],
                    $package['description'],
                    $package['activity'],
                    $package['ex'],
                    $package['reservation_date'],
                    $package['creation_date']
                ]);
            }

            $sql = 'INSERT INTO released_package
                    (owner_id, verif_id, valid_id, project_relation_id, description, activity, ex, reservation_date, creation_date)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<comment>Batch ' . ($batchIndex + 1) . '/' . count($batches) . ' inséré (' . count($batch) . ' packages)</comment>');
        }
    }

    /**
     * Construire le mapping des packages
     */
    private function buildPackageMapping(array $validPackages, OutputInterface $output): array
    {
        $mapping = [];

        // Récupérer les IDs générés en utilisant l'ordre d'insertion
        // Comme nous insérons en batch dans l'ordre, nous pouvons récupérer les derniers IDs
        $count = count($validPackages);
        $sql = "SELECT id FROM released_package ORDER BY id DESC LIMIT $count";
        $results = $this->em->getConnection()->fetchAllAssociative($sql);

        // Inverser pour avoir l'ordre d'insertion
        $results = array_reverse($results);

        // Mapper avec les legacy_num dans l'ordre
        foreach ($validPackages as $index => $package) {
            if (isset($results[$index])) {
                $mapping[$package['legacy_num']] = $results[$index]['id'];
            }
        }

        $output->writeln('<info>Mapping construit pour ' . count($mapping) . ' packages.</info>');
        return $mapping;
    }


}
